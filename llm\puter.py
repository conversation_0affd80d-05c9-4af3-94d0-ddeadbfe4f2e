import time
import json
import asyncio
import websockets
import threading
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.common.desired_capabilities import DesiredCapabilities

class PuterClient:
    def __init__(self):
        self.connected_clients = set()
        self.driver = None
        self.server_thread = None
        
    async def handle_client(self, websocket, path):
        self.connected_clients.add(websocket)
        print("client connected")
        try:
            await websocket.wait_closed()
        except websockets.exceptions.ConnectionClosed:
            pass
        finally:
            self.connected_clients.remove(websocket)
            print("client disconnected")

    async def handle_chat_request(self, websocket, data):
        # send chat request to browser
        response = {"type": "chat_response", "request_id": data.get("request_id")}
        await websocket.send(json.dumps(response))

    async def health_check(self):
        if self.connected_clients:
            message = json.dumps({"type": "health_check"})
            await asyncio.gather(
                *[client.send(message) for client in self.connected_clients],
                return_exceptions=True
            )
            return True
        return False

    def start_websocket_server(self):
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        start_server = websockets.serve(self.handle_client, "localhost", 8635)
        print("server starting on localhost:8635")
        loop.run_until_complete(start_server)
        loop.run_forever()

    def build_ws(self):
        # setup chrome options
        chrome_options = Options()
        chrome_options.add_argument("--disable-gpu")  # Disable GPU acceleration
        chrome_options.add_argument("--no-sandbox")  # Disable sandboxing
        chrome_options.add_argument("user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
        chrome_options.add_argument("window-size=1920x1080")  # Set a standard screen resolution
        
        capabilities = DesiredCapabilities.CHROME.copy()
        capabilities['acceptSslCerts'] = True
        capabilities['acceptInsecureCerts'] = True
        capabilities['browserVersion'] = "91.0"
        capabilities['platformName'] = "Windows 10"
        self.driver = webdriver.Chrome(options=chrome_options)
        
        # start websocket server in background thread
        self.server_thread = threading.Thread(target=self.start_websocket_server, daemon=True)
        self.server_thread.start()
        time.sleep(1)  # let server start

        # goto playground
        self.driver.get("https://docs.puter.com/playground/")
        time.sleep(1)  # allow page to load

        # read script from file
        with open('script.js', 'r') as file:
            js_content = file.read()

        # inject script
        html_template = """<html>
<body>
    <script src="https://js.puter.com/v2/"></script>
    <script>
        {}
    </script>
</body>
</html>"""

        # inject the complete html with script content
        complete_script = html_template.format(js_content)
        # escape content
        escaped_script = json.dumps(complete_script)
        self.driver.execute_script(f"editor.setValue({escaped_script});")
        time.sleep(0.4)

        # click run button
        run_button = self.driver.find_element(By.ID, "run")
        run_button.click()

        # wait for client to connect
        time.sleep(3)

        # health check via websocket
        if self.connected_clients:
            print("websocket success")
            return True
        else:
            print("websocket fail")
            return False

    def close(self):
        if self.driver:
            self.driver.quit()

# for standalone usage
if __name__ == "__main__":
    client = PuterClient()
    try:
        client.build_ws()
        input("[enter] to quit..")
    except Exception as e:
        print(f"oops: {e}")
    finally:
        client.close()
