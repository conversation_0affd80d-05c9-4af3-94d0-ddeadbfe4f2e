#!/usr/bin/env python3
"""
Basic example demonstrating the Options Calculator API usage.
"""

import sys
import os
from datetime import datetime, timedelta

# Add the parent directory to the path to import options_calculator
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from options_calculator import LongCallCalculator, LongPutCalculator


def main():
    """Demonstrate basic API usage."""
    print("Options Profit Calculator API - Basic Example")
    print("=" * 50)

    # Set up expiry date (30 days from now)
    expiry_date = (datetime.now() + timedelta(days=30)).strftime('%Y-%m-%d')

    # Long Call Example
    print("\n1. Long Call Example (AAPL)")
    print("-" * 30)

    call_calc = LongCallCalculator()
    call_result = call_calc.calculate(
        symbol="AAPL",
        current_price=150.00,
        strike_price=155.00,
        option_premium=3.50,
        expiry_date=expiry_date,
        contracts=1
    )

    print(f"Strategy: {call_result.strategy}")
    print(f"Break-even: ${call_result.breakeven:.2f}")
    print(f"Max Profit: {'Unlimited' if call_result.max_profit is None else f'${call_result.max_profit:.2f}'}")
    print(f"Max Loss: ${call_result.max_loss:.2f}")
    print(f"Total Cost: ${call_result.total_cost:.2f}")
    if call_result.probability_of_profit:
        print(f"Probability of Profit: {call_result.probability_of_profit:.1f}%")

    # Long Put Example
    print("\n2. Long Put Example (AAPL)")
    print("-" * 30)

    put_calc = LongPutCalculator()
    put_result = put_calc.calculate(
        symbol="AAPL",
        current_price=150.00,
        strike_price=145.00,
        option_premium=2.75,
        expiry_date=expiry_date,
        contracts=1
    )

    print(f"Strategy: {put_result.strategy}")
    print(f"Break-even: ${put_result.breakeven:.2f}")
    print(f"Max Profit: ${put_result.max_profit:.2f}")
    print(f"Max Loss: ${put_result.max_loss:.2f}")
    print(f"Total Cost: ${put_result.total_cost:.2f}")
    if put_result.probability_of_profit:
        print(f"Probability of Profit: {put_result.probability_of_profit:.1f}%")

    print("\n" + "=" * 50)
    print("Example completed successfully!")


if __name__ == "__main__":
    main()
