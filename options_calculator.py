"""
Options Profit Calculator API

A simplified Python API for calculating Long Call and Long Put options profit/loss scenarios.
"""

import math
from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any
import numpy as np
from scipy.stats import norm


class OptionCalculationResult:
    """Result model for option calculations."""
    
    def __init__(self, strategy: str, symbol: str, current_price: float, 
                 strike_price: float, option_premium: float, contracts: int,
                 total_cost: float, breakeven: float, max_profit: Optional[float],
                 max_loss: float, probability_of_profit: Optional[float],
                 scenarios: List[Dict], raw_data: Dict[str, Any]):
        self.strategy = strategy
        self.symbol = symbol
        self.current_price = current_price
        self.strike_price = strike_price
        self.option_premium = option_premium
        self.contracts = contracts
        self.total_cost = total_cost
        self.breakeven = breakeven
        self.max_profit = max_profit
        self.max_loss = max_loss
        self.probability_of_profit = probability_of_profit
        self.scenarios = scenarios
        self.raw_data = raw_data


class BaseOptionsCalculator:
    """Base class for options calculators."""
    
    def _validate_inputs(self, symbol: str, current_price: float, strike_price: float,
                        option_premium: float, expiry_date: str, contracts: int) -> None:
        """Validate calculation inputs."""
        if not symbol or not symbol.strip():
            raise ValueError("Symbol cannot be empty")
        
        if current_price <= 0:
            raise ValueError("Current price must be positive")
        
        if strike_price <= 0:
            raise ValueError("Strike price must be positive")
        
        if option_premium <= 0:
            raise ValueError("Option premium must be positive")
        
        if contracts <= 0:
            raise ValueError("Number of contracts must be positive")
        
        # Validate expiry date is in the future
        try:
            expiry = datetime.strptime(expiry_date, '%Y-%m-%d')
            if expiry <= datetime.now():
                raise ValueError("Expiry date must be in the future")
        except ValueError as e:
            if "does not match format" in str(e):
                raise ValueError("Invalid expiry date format. Use YYYY-MM-DD")
            raise
    
    def _calculate_time_to_expiry(self, expiry_date: str) -> float:
        """Calculate time to expiry in years."""
        expiry = datetime.strptime(expiry_date, '%Y-%m-%d')
        now = datetime.now()
        days_to_expiry = (expiry - now).days
        return max(days_to_expiry / 365.0, 0.001)  # Minimum 0.001 years
    
    def _calculate_implied_volatility(self, option_price: float, S: float, K: float, 
                                    T: float, r: float, option_type: str) -> float:
        """Calculate implied volatility using Newton-Raphson method."""
        sigma = 0.3  # Initial guess
        
        for _ in range(50):  # Maximum iterations
            if option_type.lower() == 'call':
                price = self._black_scholes_call(S, K, T, r, sigma)
            else:
                price = self._black_scholes_put(S, K, T, r, sigma)
            
            # Vega calculation
            d1 = (np.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * np.sqrt(T))
            vega = S * norm.pdf(d1) * np.sqrt(T)
            
            diff = price - option_price
            
            if abs(diff) < 0.001 or vega == 0:
                break
            
            sigma = max(sigma - diff / vega, 0.001)
        
        return sigma
    
    def _black_scholes_call(self, S: float, K: float, T: float, r: float, sigma: float) -> float:
        """Calculate Black-Scholes call option price."""
        if T <= 0:
            return max(S - K, 0)
        
        d1 = (np.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        call_price = S * norm.cdf(d1) - K * np.exp(-r * T) * norm.cdf(d2)
        return max(call_price, 0)
    
    def _black_scholes_put(self, S: float, K: float, T: float, r: float, sigma: float) -> float:
        """Calculate Black-Scholes put option price."""
        if T <= 0:
            return max(K - S, 0)
        
        d1 = (np.log(S / K) + (r + 0.5 * sigma ** 2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        
        put_price = K * np.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
        return max(put_price, 0)
    
    def _generate_profit_loss_scenarios(self, current_price: float, strike_price: float,
                                      option_premium: float, contracts: int, 
                                      option_type: str) -> List[Dict]:
        """Generate profit/loss scenarios at different stock prices."""
        scenarios = []
        
        # Generate price range around current price
        price_range = np.linspace(
            current_price * 0.5,  # 50% below current
            current_price * 1.5,  # 50% above current
            21  # 21 data points
        )
        
        total_cost = option_premium * contracts * 100
        
        for stock_price in price_range:
            if option_type.lower() == 'call':
                # Long call profit/loss
                intrinsic_value = max(stock_price - strike_price, 0)
                option_value = intrinsic_value * contracts * 100
                profit_loss = option_value - total_cost
            else:
                # Long put profit/loss
                intrinsic_value = max(strike_price - stock_price, 0)
                option_value = intrinsic_value * contracts * 100
                profit_loss = option_value - total_cost
            
            profit_loss_percent = (profit_loss / total_cost) * 100 if total_cost > 0 else 0
            
            scenarios.append({
                'stock_price': round(stock_price, 2),
                'profit_loss': round(profit_loss, 2),
                'profit_loss_percent': round(profit_loss_percent, 2),
                'option_value': round(option_value, 2)
            })
        
        return scenarios


class LongCallCalculator(BaseOptionsCalculator):
    """Calculator for Long Call options."""
    
    def calculate(self, symbol: str, current_price: float, strike_price: float,
                 option_premium: float, expiry_date: str, contracts: int = 1,
                 implied_volatility: Optional[float] = None) -> OptionCalculationResult:
        """
        Calculate Long Call option profit/loss scenarios.
        
        Args:
            symbol: Stock symbol
            current_price: Current stock price
            strike_price: Option strike price
            option_premium: Option premium/cost
            expiry_date: Expiration date (YYYY-MM-DD)
            contracts: Number of contracts (default: 1)
            implied_volatility: Implied volatility (optional)
            
        Returns:
            OptionCalculationResult with calculation results
        """
        # Validate inputs
        self._validate_inputs(symbol, current_price, strike_price, 
                            option_premium, expiry_date, contracts)
        
        # Calculate key metrics
        total_cost = option_premium * contracts * 100
        breakeven = strike_price + option_premium
        max_loss = total_cost  # Limited to premium paid
        max_profit = None  # Unlimited for long calls
        
        # Generate profit/loss scenarios
        scenarios = self._generate_profit_loss_scenarios(
            current_price, strike_price, option_premium, contracts, 'call'
        )
        
        # Calculate probability metrics
        time_to_expiry = self._calculate_time_to_expiry(expiry_date)
        
        # If implied volatility not provided, estimate it
        if implied_volatility is None:
            implied_volatility = self._calculate_implied_volatility(
                option_premium, current_price, strike_price, time_to_expiry, 0.02, 'call'
            )
        
        # Calculate probability of profit (stock price > breakeven at expiry)
        d = (np.log(current_price / breakeven) + (0.02 - 0.5 * implied_volatility ** 2) * time_to_expiry) / (implied_volatility * np.sqrt(time_to_expiry))
        probability_of_profit = norm.cdf(d) * 100
        
        return OptionCalculationResult(
            strategy="Long Call",
            symbol=symbol.upper(),
            current_price=current_price,
            strike_price=strike_price,
            option_premium=option_premium,
            contracts=contracts,
            total_cost=total_cost,
            breakeven=round(breakeven, 2),
            max_profit=max_profit,
            max_loss=round(max_loss, 2),
            probability_of_profit=round(probability_of_profit, 2),
            scenarios=scenarios,
            raw_data={
                'implied_volatility': round(implied_volatility, 4),
                'time_to_expiry': round(time_to_expiry, 4)
            }
        )


class LongPutCalculator(BaseOptionsCalculator):
    """Calculator for Long Put options."""
    
    def calculate(self, symbol: str, current_price: float, strike_price: float,
                 option_premium: float, expiry_date: str, contracts: int = 1,
                 implied_volatility: Optional[float] = None) -> OptionCalculationResult:
        """
        Calculate Long Put option profit/loss scenarios.
        
        Args:
            symbol: Stock symbol
            current_price: Current stock price
            strike_price: Option strike price
            option_premium: Option premium/cost
            expiry_date: Expiration date (YYYY-MM-DD)
            contracts: Number of contracts (default: 1)
            implied_volatility: Implied volatility (optional)
            
        Returns:
            OptionCalculationResult with calculation results
        """
        # Validate inputs
        self._validate_inputs(symbol, current_price, strike_price, 
                            option_premium, expiry_date, contracts)
        
        # Calculate key metrics
        total_cost = option_premium * contracts * 100
        breakeven = strike_price - option_premium
        max_loss = total_cost  # Limited to premium paid
        max_profit = (strike_price - option_premium) * contracts * 100  # Maximum when stock goes to $0
        
        # Generate profit/loss scenarios
        scenarios = self._generate_profit_loss_scenarios(
            current_price, strike_price, option_premium, contracts, 'put'
        )
        
        # Calculate probability metrics
        time_to_expiry = self._calculate_time_to_expiry(expiry_date)
        
        # If implied volatility not provided, estimate it
        if implied_volatility is None:
            implied_volatility = self._calculate_implied_volatility(
                option_premium, current_price, strike_price, time_to_expiry, 0.02, 'put'
            )
        
        # Calculate probability of profit (stock price < breakeven at expiry)
        d = (np.log(current_price / breakeven) + (0.02 - 0.5 * implied_volatility ** 2) * time_to_expiry) / (implied_volatility * np.sqrt(time_to_expiry))
        probability_of_profit = (1 - norm.cdf(d)) * 100
        
        return OptionCalculationResult(
            strategy="Long Put",
            symbol=symbol.upper(),
            current_price=current_price,
            strike_price=strike_price,
            option_premium=option_premium,
            contracts=contracts,
            total_cost=total_cost,
            breakeven=round(breakeven, 2),
            max_profit=round(max_profit, 2),
            max_loss=round(max_loss, 2),
            probability_of_profit=round(probability_of_profit, 2),
            scenarios=scenarios,
            raw_data={
                'implied_volatility': round(implied_volatility, 4),
                'time_to_expiry': round(time_to_expiry, 4)
            }
        )
